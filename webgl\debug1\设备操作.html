<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备操作 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="../logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入通用参数配置样式 -->
    <link rel="stylesheet" href="../common/parameter-config.css">
    <style>
        /* 设备操作页面专用样式 - 使用统一CSS风格 */

        .control-button {
            width: 80px;
            height: 35px;
            border: 1px solid rgba(0, 212, 255, 0.4);
            border-radius: 18px;
            background: rgba(42, 49, 66, 0.7);
            font-size: 14px;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 2px;
        }

        .start-button:hover {
            background: rgba(76, 175, 80, 1);
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }

        .stop-button:hover {
            background: rgba(244, 67, 54, 1);
            box-shadow: 0 0 10px rgba(244, 67, 54, 0.5);
        }

        .reset-button:hover {
            background: rgba(33, 150, 243, 1);
            box-shadow: 0 0 10px rgba(33, 150, 243, 0.5);
        }

        .option-button {
            width: 80px;
            height: 35px;
            border: 1px solid rgba(0, 212, 255, 0.4);
            border-radius: 18px;
            background: rgba(42, 49, 66, 0.7);
            color: white;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 2px;
        }

        .option-button:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: rgba(0, 212, 255, 0.6);
        }

        .option-button.active {
            background: rgba(0, 212, 255, 0.8);
            border-color: #00d4ff;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .status-indicator {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #666;
            border: 2px solid #333;
            transition: all 0.3s ease;
            display: inline-block;
            margin-left: 10px;
        }

        .status-indicator.active {
            background: #4CAF50;
            border-color: #45a049;
            box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
        }

        .status-indicator.stop {
            background: #f44336;
            border-color: #da190b;
            box-shadow: 0 0 8px rgba(244, 67, 54, 0.5);
        }

        .button-cell {
            text-align: center;
            vertical-align: middle;
        }

        .status-cell {
            text-align: center;
            vertical-align: middle;
        }

        /* 使用通用的status-message样式，与保护使能页面保持一致 */
    </style>
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>设备操作</h1>
        </div>

        <div class="main-content">
            <!-- 运行状态面板 -->
            <div class="protection-panel">
                <div class="panel-title">运行状态</div>
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>操作</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="button-cell">
                                <button class="control-button start-button" onclick="startOperation()">启动</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="start-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="control-button stop-button" onclick="stopOperation()">停止</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator stop" id="stop-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="control-button reset-button" onclick="resetOperation()">复位</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="reset-indicator"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 主控方式面板 -->
            <div class="protection-panel">
                <div class="panel-title">主控方式</div>
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>控制模式</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="fixedCompensation" onclick="selectMainControl(this)">固定补偿</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="fixed-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="dynamicCompensation" onclick="selectMainControl(this)">动态补偿</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="dynamic-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="powerFactor" onclick="selectMainControl(this)">功率因数</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="power-indicator"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 辅控方式面板 -->
            <div class="protection-panel">
                <div class="panel-title">辅控方式</div>
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>控制模式</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="harmonicElimination" onclick="selectAuxiliaryControl(this)">谐波消除</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="harmonic-indicator"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>

    <script>
        // 设备操作页面的 JavaScript 逻辑
        let currentMainControl = null;
        let currentAuxiliaryControl = null;
        let mqttClients = {};

        let currentButtonStates = {
            start: false,
            stop: false,
            reset: false,
            harmonicElimination: false
        };

        // MQTT 配置
        const mqttConfigs = {
            control: {
                subscribeTopic: "/201/D196HBJWP3B1F/ws/service",
                reportTopic: "/201/D196HBJWP3B1F/function/get",
                port: "1883"
            },
            alarm: {
                subscribeTopic: "/198/D195FEVZWYR85/ws/service",
                reportTopic: "/198/D195FEVZWYR85/function/get",
                port: "1883"
            },
            debug: {
                subscribeTopic: "/197/D19EOEN59V1MJ/ws/service",
                reportTopic: "/197/D19EOEN59V1MJ/function/get",
                port: "1883"
            }
        };

        // 初始化MQTT连接 - 借鉴保护使能页面的连接方式
        function initMQTTConnections() {
            console.log('初始化MQTT连接...');
            showStatusMessage('正在连接MQTT服务器...', 'warning');

            // 使用与保护使能页面相同的MQTT配置
            const options = {
                username: 'FastBee',
                password: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjdhM2RjZWY1LTE5ODgtNDg4OS04OTAzLTIwY2I0YjIyZDA0YSJ9.g1HCISIvQd6YkNgWhblKnXqHeRI74lnP1F8qZOd9XV5a7J41Qi77f9jLxxWd_EVN0XJPP1haYeRK3Uz_xrbEuA',
                cleanSession: true,
                keepAlive: 30,
                clientId: 'web-' + Math.random().toString(16).substr(2),
                connectTimeout: 60000,
            };

            // 使用已验证的MQTT服务器地址
            const brokerUrl = 'wss://mqtt.qizhiyun.cc/mqtt';
            console.log('连接到MQTT服务器:', brokerUrl);

            Object.keys(mqttConfigs).forEach(key => {
                connectMQTTClient(key, brokerUrl, options);
            });
        }

        // 连接单个MQTT客户端
        function connectMQTTClient(clientKey, brokerUrl, options) {
            const config = mqttConfigs[clientKey];

            // 为每个客户端创建唯一的clientId
            const clientOptions = {
                ...options,
                clientId: `web-${Date.now()}-${Math.random().toString(16).substr(2)}`
            };

            const client = mqtt.connect(brokerUrl, clientOptions);

            client.on('connect', () => {
                console.log(`🔗 MQTT ${clientKey} 连接成功`);
                console.log(`📡 准备订阅主题: ${config.subscribeTopic}`);
                client.subscribe(config.subscribeTopic, { qos: 1 }, (err) => {
                    if (!err) {
                        console.log(`✅ ${clientKey} 订阅主题成功: ${config.subscribeTopic}`);
                        console.log(`🎯 ${clientKey} 客户端等待接收数据...`);
                    } else {
                        console.error(`❌ ${clientKey} 订阅主题失败: ${config.subscribeTopic}`, err);
                    }
                });
                updateMQTTStatus();
                showStatusMessage(`MQTT ${clientKey} 连接成功`, 'success');
            });

            client.on('message', (topic, message) => {
                console.log(`📨 收到 ${clientKey} 主题消息:`, topic);
                console.log(`📄 ${clientKey} 消息内容长度:`, message.toString().length);
                try {
                    const data = JSON.parse(message.toString());
                    console.log(`✅ ${clientKey} 消息解析成功，数据条目数:`, Object.keys(data).length);
                    handleMQTTMessage(clientKey, data.message);
                } catch (error) {
                    console.error(`❌ ${clientKey} 解析MQTT消息失败:`, error);
                    console.log(`🔍 ${clientKey} 原始消息:`, message.toString().substring(0, 200));
                }
            });

            client.on('error', (error) => {
                console.error(`MQTT ${clientKey} 连接错误:`, error);
                updateMQTTStatus(false);
                showStatusMessage(`MQTT ${clientKey} 连接失败: ${error.message}`, 'error');
            });

            client.on('close', () => {
                console.log(`MQTT ${clientKey} 连接已断开`);
                updateMQTTStatus(false);
            });

            client.on('offline', () => {
                console.log(`MQTT ${clientKey} 离线`);
                updateMQTTStatus(false);
            });

            mqttClients[clientKey] = client;
        }

        // 显示MQTT连接状态
        function showMQTTConnectionStatus() {
            console.log('🔍 MQTT连接状态检查:');
            Object.keys(mqttConfigs).forEach(key => {
                const client = mqttClients[key];
                const config = mqttConfigs[key];
                if (client) {
                    console.log(`${key}: 连接状态=${client.connected ? '✅已连接' : '❌未连接'}, 主题=${config.subscribeTopic}`);
                } else {
                    console.log(`${key}: ❌客户端未创建`);
                }
            });
        }

        // 处理MQTT消息
        function handleMQTTMessage(clientType, data) {
            console.log(`📥 收到${clientType}消息:`, data);
            
            // 声明新的data变量，将原data中的id作为key，value作为值
            const newData = {};
            if (Array.isArray(data)) {
                // 如果data是数组格式
                data.forEach(item => {
                    if (item.id !== undefined && item.value !== undefined) {
                        newData[item.id] = item.value;
                        // console.log(item.id, item.value);
                    }
                });
            } else if (typeof data === 'object' && data !== null) {
                // 如果data已经是对象格式，直接使用
                Object.assign(newData, data);
            }
            
            // 使用新的data变量进行后续处理
            data = newData;
            
            if (clientType === 'alarm') {
                // 处理开关量报警消息，更新状态灯
                updateStatusFromAlarm(data);
            } else if (clientType === 'debug') {
                // 处理调试参数消息，更新主控方式状态灯
                updateMainControlStatus(data);
                console.log(data)
            }

            // 更新数据时间戳
            document.getElementById('data-timestamp').textContent = new Date().toLocaleString();
        }

        // 根据开关量报警更新状态灯
        function updateStatusFromAlarm(data) {
            if (data['39_5'] !== undefined) {
                updateStatusIndicator('start-indicator', data['39_5'] == 1);
            }
            if (data['39_6'] !== undefined || data['39_11'] !== undefined) {
                const resetActive = data['39_6'] == 1 || data['39_11'] == 1;
                updateStatusIndicator('reset-indicator', resetActive);
            }
        }

        // 根据调试参数更新主控方式状态灯（去掉电压控制和无功电压）
        function updateMainControlStatus(data) {
            const om = data['1914_OM'];
            const rpc = data['1914_RPC'];

            // 添加调试日志
            console.log('主控状态更新 - 1914_OM:', om, '1914_RPC:', rpc);
            console.log('接收到的完整数据:', data);

            // 清除所有主控方式状态灯（去掉电压控制和无功电压）
            ['fixed-indicator', 'dynamic-indicator', 'power-indicator'].forEach(id => {
                updateStatusIndicator(id, false);
            });

            // 根据参数值设置对应状态灯（去掉电压控制和无功电压）
            if (rpc == 1) {
                console.log('设置固定补偿状态灯亮');
                updateStatusIndicator('fixed-indicator', true);
            } else if (rpc == 0) {
                if (om == 0) {
                    console.log('设置动态补偿状态灯亮');
                    updateStatusIndicator('dynamic-indicator', true);
                } else if (om == 1) {
                    console.log('设置功率因素状态灯亮');
                    updateStatusIndicator('power-indicator', true);
                }
            }
        }

        // 更新MQTT连接状态
        function updateMQTTStatus(connected = true) {
            const mqttStatus = document.getElementById('mqtt-status');
            const connectedCount = Object.values(mqttClients).filter(client => client.connected).length;

            if (connectedCount === Object.keys(mqttConfigs).length) {
                mqttStatus.textContent = 'MQTT 全部连接';
                mqttStatus.className = 'mqtt-connection-status connected';
            } else if (connectedCount > 0) {
                mqttStatus.textContent = `MQTT 部分连接 (${connectedCount}/${Object.keys(mqttConfigs).length})`;
                mqttStatus.className = 'mqtt-connection-status connecting';
            } else {
                mqttStatus.textContent = 'MQTT 连接断开';
                mqttStatus.className = 'mqtt-connection-status disconnected';
            }
        }

        // 发送MQTT消息
        function sendMQTTMessage(clientType, data) {
            const client = mqttClients[clientType];
            const config = mqttConfigs[clientType];

            if (!client) {
                console.error(`MQTT ${clientType} 客户端不存在`);
                showStatusMessage(`MQTT ${clientType} 客户端未初始化`, 'error');
                return;
            }

            if (!client.connected) {
                console.error(`MQTT ${clientType} 客户端未连接`);
                showStatusMessage(`MQTT ${clientType} 连接断开，正在尝试重连...`, 'warning');

                // 尝试重新连接
                if (client.reconnecting) {
                    showStatusMessage('MQTT正在重连中，请稍后再试', 'warning');
                } else {
                    client.reconnect();
                }
                return;
            }

            const message = JSON.stringify(data);
            client.publish(config.reportTopic, message, (err) => {
                if (err) {
                    console.error('发送MQTT消息失败:', err);
                    showStatusMessage(`发送${clientType}消息失败: ${err.message}`, 'error');
                } else {
                    console.log(`发送${clientType}消息成功:`, data);
                    showStatusMessage(`${clientType}指令发送成功`, 'success');
                }
            });
        }

        // 启动操作
        function startOperation() {
            console.log('启动操作');

            // 更新按钮状态
            currentButtonStates.start = true;
            currentButtonStates.stop = false;

            // 更新按钮样式
            updateButtonStyle('start-button', true);
            updateButtonStyle('stop-button', false);

            // 立即发送控制指令
            sendMQTTMessage('control', [{"id": "A1", "value": "0"}]);
        }

        // 停止操作
        function stopOperation() {
            console.log('停止操作');

            // 更新按钮状态
            currentButtonStates.start = false;
            currentButtonStates.stop = true;

            // 更新按钮样式
            updateButtonStyle('start-button', false);
            updateButtonStyle('stop-button', true);

            // 立即发送控制指令
            sendMQTTMessage('control', [{"id": "A2", "value": "1"}]);
        }

        // 复位操作
        function resetOperation() {
            console.log('复位操作');

            // 复位会重置启动和停止按钮状态
            currentButtonStates.start = false;
            currentButtonStates.stop = false;
            currentButtonStates.reset = true;

            // 更新按钮样式
            updateButtonStyle('start-button', false);
            updateButtonStyle('stop-button', false);
            updateButtonStyle('reset-button', true);

            setTimeout(() => {
                currentButtonStates.reset = false;
                updateButtonStyle('reset-button', false);
            }, 2000);

            // 立即发送控制指令
            sendMQTTMessage('control', [{"id": "A3", "value": "1"}]);
        }

        // 选择主控方式（去掉电压控制和无功电压）
        function selectMainControl(button) {
            // 清除之前的选择
            document.querySelectorAll('.protection-panel:nth-child(2) .option-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // 设置新的选择
            button.classList.add('active');
            currentMainControl = button.dataset.value;

            // 立即发送调试参数（去掉电压控制和无功电压）
            let debugData = [];
            switch (button.dataset.value) {
                case 'fixedCompensation':
                    debugData = [{"id": "1914_RPC", "value": "1"}, {"id": "1914_OM", "value": "0"}];
                    break;
                case 'dynamicCompensation':
                    debugData = [{"id": "1914_OM", "value": "0"}, {"id": "1914_RPC", "value": "0"}];
                    break;
                case 'powerFactor':
                    debugData = [{"id": "1914_OM", "value": "1"}, {"id": "1914_RPC", "value": "0"}];
                    break;
            }
            sendMQTTMessage('debug', debugData);

            console.log('选择主控方式:', button.textContent);
        }

        // 选择辅控方式（去掉无功启动和综合控制）
        function selectAuxiliaryControl(button) {
            const value = button.dataset.value;

            // 辅控方式按钮可以多选，切换状态
            const isActive = button.classList.contains('active');

            if (isActive) {
                button.classList.remove('active');
                currentButtonStates[value] = false;
            } else {
                button.classList.add('active');
                currentButtonStates[value] = true;

                // 立即发送控制指令（只保留谐波消除）
                let controlData = [];
                switch (value) {
                    case 'harmonicElimination':
                        controlData = [{"id": "A5", "value": "1"}];
                        break;
                }
                sendMQTTMessage('control', controlData);
            }

            // 更新状态灯
            const indicatorId = getIndicatorId(value, 'auxiliary');
            updateStatusIndicator(indicatorId, !isActive);

            console.log('选择辅控方式:', button.textContent);
        }

        // 获取状态指示器ID（去掉电压控制、无功电压、无功启动、综合控制）
        function getIndicatorId(value, type) {
            const mapping = {
                'fixedCompensation': 'fixed-indicator',
                'dynamicCompensation': 'dynamic-indicator',
                'powerFactor': 'power-indicator',
                'harmonicElimination': 'harmonic-indicator'
            };
            return mapping[value];
        }

        // 更新状态指示器
        function updateStatusIndicator(indicatorId, active) {
            const indicator = document.getElementById(indicatorId);
            if (indicator) {
                if (active) {
                    indicator.classList.add('active');
                } else {
                    indicator.classList.remove('active');
                }
            }
        }

        // 更新按钮样式
        function updateButtonStyle(buttonClass, active) {
            const button = document.querySelector(`.${buttonClass}`);
            if (button) {
                if (active) {
                    if (buttonClass === 'start-button') {
                        button.style.background = 'rgba(76, 175, 80, 1)';
                        button.style.boxShadow = '0 0 10px rgba(76, 175, 80, 0.5)';
                    } else if (buttonClass === 'stop-button') {
                        button.style.background = 'rgba(244, 67, 54, 1)';
                        button.style.boxShadow = '0 0 10px rgba(244, 67, 54, 0.5)';
                    } else if (buttonClass === 'reset-button') {
                        button.style.background = 'rgba(33, 150, 243, 1)';
                        button.style.boxShadow = '0 0 10px rgba(33, 150, 243, 0.5)';
                    }
                } else {
                    button.style.background = '';
                    button.style.boxShadow = '';
                }
            }
        }

        // 显示状态消息
        function showStatusMessage(message, type = 'success') {
            const statusDiv = document.getElementById('status-message');
            statusDiv.textContent = message;
            statusDiv.className = `status-message ${type}`;
            statusDiv.style.display = 'block';

            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('设备操作页面初始化...');

            // 初始化MQTT连接
            initMQTTConnections();

            // 5秒后检查连接状态
            setTimeout(() => {
                showMQTTConnectionStatus();
            }, 5000);

            // 每30秒检查一次连接状态
            setInterval(() => {
                showMQTTConnectionStatus();
            }, 30000);

            // 定期更新时间戳
            setInterval(() => {
                const connectedCount = Object.values(mqttClients).filter(client => client.connected).length;
                if (connectedCount > 0) {
                    document.getElementById('data-timestamp').textContent = new Date().toLocaleString();
                }
            }, 1000);
        });
    </script>
</body>
</html>